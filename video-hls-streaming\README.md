# Video HLS Streaming Project

A complete video streaming application that converts MP4 videos to HLS format and streams them in the browser.

## 🏗️ Project Structure

```
video-hls-streaming/
├── backend/          ← Spring Boot application
│   ├── src/
│   │   └── main/
│   │       ├── java/com/videostreaming/
│   │       │   ├── VideoStreamingApplication.java
│   │       │   ├── controller/VideoController.java
│   │       │   ├── service/VideoProcessingService.java
│   │       │   └── config/WebConfig.java
│   │       └── resources/
│   │           └── application.properties
│   ├── uploads/      ← Temporary upload directory
│   ├── hls/          ← HLS output directory
│   └── pom.xml
└── frontend/         ← React application
    ├── src/
    │   ├── components/
    │   │   ├── VideoUpload.jsx
    │   │   └── VideoPlayer.jsx
    │   ├── App.jsx
    │   └── index.css
    ├── package.json
    └── tailwind.config.js
```

## 🔧 Prerequisites

Before running this project, make sure you have:

1. **Java 17+** installed
2. **Maven 3.6+** installed
3. **Node.js 18+** and npm installed
4. **FFmpeg** installed and available in PATH

### Installing FFmpeg

#### Windows:
1. Download FFmpeg from https://ffmpeg.org/download.html
2. Extract and add to PATH environment variable
3. Verify: `ffmpeg -version`

#### macOS:
```bash
brew install ffmpeg
```

#### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install ffmpeg
```

## 🚀 Setup Instructions

### 1. Backend Setup (Spring Boot)

```bash
# Navigate to backend directory
cd video-hls-streaming/backend

# Build the project
mvn clean install

# Run the Spring Boot application
mvn spring-boot:run
```

The backend will start on `http://localhost:8080`

### 2. Frontend Setup (React)

```bash
# Navigate to frontend directory
cd video-hls-streaming/frontend

# Install dependencies
npm install

# Start the development server
npm run dev
```

The frontend will start on `http://localhost:3000`

## 📱 How to Use

1. **Open the application** in your browser at `http://localhost:3000`

2. **Upload a video**:
   - Click "Choose MP4 video file"
   - Select an MP4 file (max 500MB)
   - Click "Upload & Convert to HLS"

3. **Watch the video**:
   - After processing, the video will automatically start playing
   - The video is streamed in HLS format with adaptive bitrate

4. **Upload another video**:
   - Click "Upload Another Video" to upload more videos

## 🔄 How It Works

1. **Upload**: User uploads MP4 video via React frontend
2. **Processing**: Spring Boot backend receives the file and saves it temporarily
3. **Conversion**: FFmpeg converts MP4 to HLS format (`.m3u8` + `.ts` chunks)
4. **Storage**: HLS files are stored in `backend/hls/{videoId}/` directory
5. **Streaming**: Frontend uses hls.js to stream the video from backend
6. **Cleanup**: Original uploaded file is deleted after conversion

## 🛠️ API Endpoints

### POST `/api/videos/upload`
- **Description**: Upload and convert video to HLS
- **Content-Type**: `multipart/form-data`
- **Parameters**: `video` (MP4 file)
- **Response**: 
```json
{
  "videoId": "uuid-string",
  "message": "Video uploaded and processed successfully",
  "streamUrl": "/api/videos/stream/{videoId}/index.m3u8"
}
```

### GET `/api/videos/stream/{videoId}/{fileName}`
- **Description**: Stream HLS files (.m3u8 and .ts)
- **Parameters**: 
  - `videoId`: Unique video identifier
  - `fileName`: HLS file name (index.m3u8 or segment.ts)
- **Response**: HLS file content

## 🎯 Features

- ✅ MP4 to HLS conversion using FFmpeg
- ✅ Real-time upload progress
- ✅ Adaptive bitrate streaming
- ✅ Responsive UI with Tailwind CSS
- ✅ Error handling and validation
- ✅ CORS configuration for cross-origin requests
- ✅ File size validation (max 500MB)
- ✅ Automatic cleanup of temporary files

## 🔧 Configuration

### Backend Configuration (`application.properties`)
```properties
server.port=8080
spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=500MB
app.upload.dir=uploads
app.hls.dir=hls
```

### FFmpeg Command Used
```bash
ffmpeg -i input.mp4 -codec: copy -start_number 0 -hls_time 10 -hls_list_size 0 -f hls index.m3u8
```

## 🐛 Troubleshooting

### Common Issues:

1. **FFmpeg not found**:
   - Ensure FFmpeg is installed and in PATH
   - Test with: `ffmpeg -version`

2. **CORS errors**:
   - Backend includes CORS configuration for localhost:3000
   - Check if both servers are running on correct ports

3. **Video not playing**:
   - Check browser console for HLS errors
   - Ensure video was successfully converted (check backend logs)
   - Verify HLS files exist in `backend/hls/{videoId}/`

4. **Upload fails**:
   - Check file size (max 500MB)
   - Ensure file is MP4 format
   - Check backend logs for detailed error messages

## 📝 Development Notes

- Backend uses Spring Boot 3.2.0 with Java 17
- Frontend uses React 18 with Vite
- HLS.js library handles video streaming in browsers
- Tailwind CSS for responsive styling
- Axios for HTTP requests

## 🔒 Security Considerations

For production deployment, consider:
- File upload validation and sanitization
- Rate limiting for uploads
- Authentication and authorization
- Secure file storage
- HTTPS configuration
- Input validation and error handling
