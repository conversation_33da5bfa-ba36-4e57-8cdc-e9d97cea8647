package com.videostreaming.controller;

import com.videostreaming.service.VideoProcessingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

@RestController
@RequestMapping("/api/videos")
@CrossOrigin(origins = "http://localhost:3000")
public class VideoController {

    @Autowired
    private VideoProcessingService videoProcessingService;

    @Value("${app.upload.dir}")
    private String uploadDir;

    @Value("${app.hls.dir}")
    private String hlsDir;

    @PostMapping("/upload")
    public ResponseEntity<Map<String, String>> uploadVideo(@RequestParam("video") MultipartFile file) {
        Map<String, String> response = new HashMap<>();

        try {
            // Validate file
            if (file.isEmpty()) {
                response.put("error", "Please select a video file");
                return ResponseEntity.badRequest().body(response);
            }

            // Check if file is a video
            String contentType = file.getContentType();
            String fileName = file.getOriginalFilename();
            if (contentType == null || (!contentType.startsWith("video/") && !isVideoFile(fileName))) {
                response.put("error", "Please upload a valid video file (MP4, AVI, MOV, MKV, WMV, FLV, WEBM, etc.)");
                return ResponseEntity.badRequest().body(response);
            }

            // Create upload directory if it doesn't exist
            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }

            // Save uploaded file temporarily
            String originalFileName = file.getOriginalFilename();
            String tempFileName = System.currentTimeMillis() + "_" + originalFileName;
            Path tempFilePath = uploadPath.resolve(tempFileName);
            file.transferTo(tempFilePath.toFile());

            // Process video to HLS format
            String videoId = videoProcessingService.processVideoToHLS(
                    tempFilePath.toString(),
                    originalFileName);

            response.put("videoId", videoId);
            response.put("message", "Video uploaded and processed successfully");
            response.put("streamUrl", "/api/videos/stream/" + videoId + "/index.m3u8");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            e.printStackTrace();
            response.put("error", "Failed to process video: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/stream/{videoId}/{fileName}")
    public ResponseEntity<Resource> streamVideo(
            @PathVariable String videoId,
            @PathVariable String fileName) {

        try {
            // Check if video exists
            if (!videoProcessingService.videoExists(videoId)) {
                return ResponseEntity.notFound().build();
            }

            // Get file path
            Path filePath = videoProcessingService.getHLSFilePath(videoId, fileName);

            if (!Files.exists(filePath)) {
                return ResponseEntity.notFound().build();
            }

            Resource resource = new FileSystemResource(filePath);

            // Set appropriate content type
            String contentType;
            if (fileName.endsWith(".m3u8")) {
                contentType = "application/vnd.apple.mpegurl";
            } else if (fileName.endsWith(".ts")) {
                contentType = "video/mp2t";
            } else {
                contentType = "application/octet-stream";
            }

            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CACHE_CONTROL, "no-cache")
                    .body(resource);

        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/list")
    public ResponseEntity<List<Map<String, Object>>> getVideoList() {
        try {
            List<Map<String, Object>> videos = new ArrayList<>();
            Path hlsPath = Paths.get(hlsDir);

            if (Files.exists(hlsPath)) {
                Files.list(hlsPath)
                        .filter(Files::isDirectory)
                        .forEach(videoDir -> {
                            try {
                                Map<String, Object> videoInfo = new HashMap<>();
                                String videoId = videoDir.getFileName().toString();

                                // Look for the original filename in uploads directory
                                Path uploadsPath = Paths.get(uploadDir);
                                String originalName = "Unknown";
                                if (Files.exists(uploadsPath)) {
                                    try {
                                        Optional<Path> uploadFile = Files.list(uploadsPath)
                                                .filter(file -> file.getFileName().toString()
                                                        .contains(videoId.substring(0, 8)))
                                                .findFirst();
                                        if (uploadFile.isPresent()) {
                                            String fileName = uploadFile.get().getFileName().toString();
                                            originalName = fileName.substring(fileName.indexOf('_') + 1);
                                        }
                                    } catch (Exception e) {
                                        // Ignore and use default name
                                    }
                                }

                                videoInfo.put("id", videoId);
                                videoInfo.put("name", originalName);
                                videoInfo.put("streamUrl", "/api/videos/stream/" + videoId + "/index.m3u8");

                                // Get video duration and size if available
                                Path m3u8File = videoDir.resolve("index.m3u8");
                                if (Files.exists(m3u8File)) {
                                    videoInfo.put("available", true);
                                    try {
                                        long size = Files.walk(videoDir)
                                                .filter(Files::isRegularFile)
                                                .mapToLong(file -> {
                                                    try {
                                                        return Files.size(file);
                                                    } catch (Exception e) {
                                                        return 0;
                                                    }
                                                })
                                                .sum();
                                        videoInfo.put("size", size);
                                    } catch (Exception e) {
                                        videoInfo.put("size", 0);
                                    }
                                } else {
                                    videoInfo.put("available", false);
                                }

                                videos.add(videoInfo);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        });
            }

            return ResponseEntity.ok(videos);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ArrayList<>());
        }
    }

    private boolean isVideoFile(String fileName) {
        if (fileName == null)
            return false;
        String lowerFileName = fileName.toLowerCase();
        return lowerFileName.endsWith(".mp4") ||
                lowerFileName.endsWith(".avi") ||
                lowerFileName.endsWith(".mov") ||
                lowerFileName.endsWith(".mkv") ||
                lowerFileName.endsWith(".wmv") ||
                lowerFileName.endsWith(".flv") ||
                lowerFileName.endsWith(".webm") ||
                lowerFileName.endsWith(".m4v") ||
                lowerFileName.endsWith(".3gp") ||
                lowerFileName.endsWith(".ogv") ||
                lowerFileName.endsWith(".asf") ||
                lowerFileName.endsWith(".rm") ||
                lowerFileName.endsWith(".rmvb");
    }
}
