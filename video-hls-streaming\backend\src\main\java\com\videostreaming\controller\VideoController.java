package com.videostreaming.controller;

import com.videostreaming.model.User;
import com.videostreaming.model.Video;
import com.videostreaming.model.VideoView;
import com.videostreaming.dto.VideoDTO;
import com.videostreaming.repository.UserRepository;
import com.videostreaming.repository.VideoRepository;
import com.videostreaming.repository.VideoViewRepository;
import com.videostreaming.security.UserPrincipal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/videos")
public class VideoController {

    @Autowired
    private VideoRepository videoRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private VideoViewRepository videoViewRepository;

    @Value("${app.upload.dir:uploads}")
    private String uploadDir;

    @Value("${app.hls.dir:hls}")
    private String hlsDir;

    @Value("${app.thumbnails.dir:thumbnails}")
    private String thumbnailsDir;

    @GetMapping("/public")
    public ResponseEntity<List<VideoDTO>> getPublicVideos(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {

        try {
            Sort sort = sortDir.equalsIgnoreCase("desc") ? Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();

            Pageable pageable = PageRequest.of(page, size, sort);
            Page<Video> videoPage = videoRepository.findByVisibilityAndStatus(
                    Video.VideoVisibility.PUBLIC,
                    Video.VideoStatus.READY,
                    pageable);

            List<VideoDTO> videoDTOs = videoPage.getContent().stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());

            return ResponseEntity.ok(videoDTOs);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/trending")
    public ResponseEntity<List<Video>> getTrendingVideos() {
        try {
            Pageable pageable = PageRequest.of(0, 20);
            List<Video> trendingVideos = videoRepository.findTrendingVideos(pageable);
            return ResponseEntity.ok(trendingVideos);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/search")
    public ResponseEntity<List<Video>> searchVideos(
            @RequestParam String query,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        try {
            Pageable pageable = PageRequest.of(page, size);
            List<Video> videos = videoRepository.searchPublicVideos(query, pageable);
            return ResponseEntity.ok(videos);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping("/upload")
    public ResponseEntity<?> uploadVideo(
            @RequestParam("video") MultipartFile file,
            @RequestParam("title") String title,
            @RequestParam(value = "description", required = false) String description) {

        try {
            // Get current user
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !authentication.isAuthenticated()) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(Map.of("message", "Authentication required"));
            }

            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
            Optional<User> userOpt = userRepository.findById(userPrincipal.getId());

            if (!userOpt.isPresent()) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(Map.of("message", "User not found"));
            }

            User user = userOpt.get();

            // Validate file
            if (file.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(Map.of("message", "Please select a video file"));
            }

            // Validate file size (500MB)
            if (file.getSize() > 500 * 1024 * 1024) {
                return ResponseEntity.badRequest()
                        .body(Map.of("message", "File size must be less than 500MB"));
            }

            // Create directories if they don't exist
            createDirectories();

            // Generate unique filename
            String originalFilename = file.getOriginalFilename();
            String fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String uniqueFilename = UUID.randomUUID().toString() + fileExtension;

            // Save file
            Path uploadPath = Paths.get(uploadDir, uniqueFilename);
            Files.copy(file.getInputStream(), uploadPath);

            // Create video entity
            Video video = new Video();
            video.setVideoId(UUID.randomUUID().toString());
            video.setTitle(title);
            video.setDescription(description != null ? description : "");
            video.setUser(user);
            video.setVideoUrl("/uploads/" + uniqueFilename);
            video.setFileSize(file.getSize());
            video.setStatus(Video.VideoStatus.PROCESSING);
            video.setVisibility(Video.VideoVisibility.PUBLIC);
            video.setCreatedAt(LocalDateTime.now());
            video.setUpdatedAt(LocalDateTime.now());

            Video savedVideo = videoRepository.save(video);

            // TODO: Process video for HLS streaming in background
            // For now, just mark as ready
            savedVideo.setStatus(Video.VideoStatus.READY);
            savedVideo.setStreamUrl("/hls/" + savedVideo.getVideoId() + "/playlist.m3u8");
            videoRepository.save(savedVideo);

            return ResponseEntity.ok(Map.of(
                    "message", "Video uploaded successfully",
                    "videoId", savedVideo.getVideoId()));

        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("message", "Upload failed: " + e.getMessage()));
        }
    }

    @GetMapping("/{videoId}")
    public ResponseEntity<Video> getVideo(@PathVariable String videoId) {
        try {
            Optional<Video> videoOpt = videoRepository.findByVideoId(videoId);
            if (!videoOpt.isPresent()) {
                return ResponseEntity.notFound().build();
            }

            Video video = videoOpt.get();

            // Increment view count
            video.setViewCount(video.getViewCount() + 1);
            videoRepository.save(video);

            // Record view if user is authenticated
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.isAuthenticated()) {
                try {
                    UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
                    Optional<User> userOpt = userRepository.findById(userPrincipal.getId());

                    if (userOpt.isPresent()) {
                        VideoView view = new VideoView();
                        view.setVideo(video);
                        view.setUser(userOpt.get());
                        view.setCreatedAt(LocalDateTime.now());
                        videoViewRepository.save(view);
                    }
                } catch (Exception e) {
                    // Continue even if view recording fails
                }
            }

            return ResponseEntity.ok(video);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/stream/{videoId}/{filename}")
    public ResponseEntity<Resource> streamVideo(
            @PathVariable String videoId,
            @PathVariable String filename) {

        try {
            Path filePath = Paths.get(hlsDir, videoId, filename);
            Resource resource = new UrlResource(filePath.toUri());

            if (resource.exists() && resource.isReadable()) {
                String contentType = Files.probeContentType(filePath);
                if (contentType == null) {
                    if (filename.endsWith(".m3u8")) {
                        contentType = "application/vnd.apple.mpegurl";
                    } else if (filename.endsWith(".ts")) {
                        contentType = "video/mp2t";
                    } else {
                        contentType = "application/octet-stream";
                    }
                }

                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .header(HttpHeaders.CACHE_CONTROL, "no-cache")
                        .body(resource);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/thumbnail/{videoId}")
    public ResponseEntity<Resource> getThumbnail(@PathVariable String videoId) {
        try {
            Path thumbnailPath = Paths.get(thumbnailsDir, videoId + ".jpg");
            Resource resource = new UrlResource(thumbnailPath.toUri());

            if (resource.exists() && resource.isReadable()) {
                return ResponseEntity.ok()
                        .contentType(MediaType.IMAGE_JPEG)
                        .body(resource);
            } else {
                // Return a default placeholder thumbnail (1x1 transparent pixel)
                byte[] defaultThumbnail = new byte[] {
                        (byte) 0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44,
                        0x52,
                        0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15,
                        (byte) 0xC4,
                        (byte) 0x89, 0x00, 0x00, 0x00, 0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, (byte) 0x9C, 0x63, 0x00,
                        0x01, 0x00,
                        0x00, 0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, (byte) 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4E,
                        0x44,
                        (byte) 0xAE, 0x42, 0x60, (byte) 0x82
                };
                return ResponseEntity.ok()
                        .contentType(MediaType.IMAGE_PNG)
                        .body(new org.springframework.core.io.ByteArrayResource(defaultThumbnail));
            }
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    private void createDirectories() {
        try {
            Files.createDirectories(Paths.get(uploadDir));
            Files.createDirectories(Paths.get(hlsDir));
            Files.createDirectories(Paths.get(thumbnailsDir));
        } catch (IOException e) {
            throw new RuntimeException("Could not create upload directories", e);
        }
    }

    private VideoDTO convertToDTO(Video video) {
        VideoDTO dto = new VideoDTO();

        // Copy video properties
        dto.setId(video.getId());
        dto.setVideoId(video.getVideoId());
        dto.setTitle(video.getTitle());
        dto.setDescription(video.getDescription());
        dto.setThumbnailUrl(video.getThumbnailUrl());
        dto.setStreamUrl(video.getStreamUrl());
        dto.setVideoUrl(video.getVideoUrl());
        dto.setDurationSeconds(video.getDurationSeconds());
        dto.setViewCount(video.getViewCount());
        dto.setLikeCount(video.getLikeCount());
        dto.setDislikeCount(video.getDislikeCount());
        dto.setCommentCount(video.getCommentCount());
        dto.setVisibility(video.getVisibility() != null ? video.getVisibility().name() : null);
        dto.setStatus(video.getStatus() != null ? video.getStatus().name() : null);
        dto.setCategory(video.getCategory());
        dto.setLanguage(video.getLanguage());
        dto.setTags(video.getTags());
        dto.setAllowsComments(video.getAllowsComments());
        dto.setAllowsRatings(video.getAllowsRatings());
        dto.setIsAgeRestricted(video.getIsAgeRestricted());
        dto.setAvailableQualities(video.getAvailableQualities());
        dto.setOriginalFilename(video.getOriginalFilename());
        dto.setFileSize(video.getFileSize());
        dto.setCreatedAt(video.getCreatedAt());
        dto.setUpdatedAt(video.getUpdatedAt());
        dto.setPublishedAt(video.getPublishedAt());

        // Copy user properties (flattened to avoid circular reference)
        if (video.getUser() != null) {
            dto.setUserId(video.getUser().getId());
            dto.setUsername(video.getUser().getUsername());
            dto.setDisplayName(video.getUser().getDisplayName());
            dto.setProfilePicture(video.getUser().getProfilePicture());
            dto.setIsVerified(video.getUser().getIsVerified());
            dto.setSubscriberCount(video.getUser().getSubscriberCount());
        }

        return dto;
    }
}
