package com.videostreaming.repository;

import com.videostreaming.model.Video;
import com.videostreaming.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface VideoRepository extends JpaRepository<Video, Long> {

    Optional<Video> findByVideoId(String videoId);

    List<Video> findByUser(User user);

    @Query("SELECT v FROM Video v WHERE v.visibility = 'PUBLIC' AND v.status = 'READY' ORDER BY v.createdAt DESC")
    Page<Video> findPublicVideos(Pageable pageable);

    @Query("SELECT v FROM Video v WHERE v.visibility = 'PUBLIC' AND v.status = 'READY' ORDER BY v.viewCount DESC")
    List<Video> findTrendingVideos(Pageable pageable);

    @Query("SELECT v FROM Video v WHERE v.visibility = 'PUBLIC' AND v.status = 'READY' AND " +
            "(v.title LIKE %:query% OR v.description LIKE %:query% OR v.tags LIKE %:query%)")
    Page<Video> searchVideos(@Param("query") String query, Pageable pageable);

    @Query("SELECT v FROM Video v WHERE v.user = :user ORDER BY v.createdAt DESC")
    Page<Video> findByUserOrderByCreatedAtDesc(@Param("user") User user, Pageable pageable);

    @Query("SELECT v FROM Video v WHERE v.category = :category AND v.visibility = 'PUBLIC' AND v.status = 'READY' ORDER BY v.viewCount DESC")
    Page<Video> findByCategoryOrderByViewCountDesc(@Param("category") String category, Pageable pageable);

    @Query("SELECT v FROM Video v WHERE v.user IN :users AND v.visibility = 'PUBLIC' AND v.status = 'READY' ORDER BY v.createdAt DESC")
    Page<Video> findSubscriptionFeed(@Param("users") List<User> users, Pageable pageable);

    // Additional methods needed by VideoController
    Page<Video> findByVisibilityAndStatus(Video.VideoVisibility visibility, Video.VideoStatus status,
            Pageable pageable);

    @Query("SELECT v FROM Video v WHERE v.visibility = 'PUBLIC' AND v.status = 'READY' AND " +
            "(v.title LIKE %:query% OR v.description LIKE %:query%)")
    List<Video> searchPublicVideos(@Param("query") String query, Pageable pageable);
}
