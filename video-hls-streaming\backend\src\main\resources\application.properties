server.port=8080
spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=500MB

# File upload paths (absolute paths)
app.upload.dir=${user.dir}/uploads
app.hls.dir=${user.dir}/hls

# CORS configuration
spring.web.cors.allowed-origins=http://localhost:3000
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
spring.web.cors.allow-credentials=true
