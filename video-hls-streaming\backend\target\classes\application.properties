# Database Configuration - MySQL
spring.datasource.url=***********************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true

# File Upload Configuration
spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=500MB
spring.servlet.multipart.enabled=true

# Application Configuration
server.port=8080
app.upload.dir=uploads
app.hls.dir=hls
app.thumbnails.dir=thumbnails

# JWT Configuration
app.jwt.secret=mySecretKey123456789012345678901234567890
app.jwt.expiration=86400000

# CORS Configuration
app.cors.allowed-origins=http://localhost:3000,http://localhost:5173,http://localhost:5174

# Logging Configuration
logging.level.com.videostreaming=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE

# Video Processing Configuration
app.video.max-size=500MB
app.video.allowed-formats=mp4,avi,mov,mkv,wmv,flv,webm,m4v,3gp,ogv

# HLS Configuration
app.hls.segment-duration=10
app.hls.qualities=360p,480p,720p,1080p

# Thumbnail Configuration
app.thumbnail.width=320
app.thumbnail.height=180
app.thumbnail.format=jpg
