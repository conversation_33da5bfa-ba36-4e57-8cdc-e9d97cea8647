{"version": 3, "names": ["_rewriteStackTrace", "require", "ConfigError", "Error", "constructor", "message", "filename", "expectedError", "injectVirtualStackFrame", "exports", "default"], "sources": ["../../src/errors/config-error.ts"], "sourcesContent": ["import {\n  injectVirtualStackFrame,\n  expectedError,\n} from \"./rewrite-stack-trace.ts\";\n\nexport default class ConfigError extends Error {\n  constructor(message: string, filename?: string) {\n    super(message);\n    expectedError(this);\n    if (filename) injectVirtualStackFrame(this, filename);\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAKe,MAAMC,WAAW,SAASC,KAAK,CAAC;EAC7CC,WAAWA,CAACC,OAAe,EAAEC,QAAiB,EAAE;IAC9C,KAAK,CAACD,OAAO,CAAC;IACd,IAAAE,gCAAa,EAAC,IAAI,CAAC;IACnB,IAAID,QAAQ,EAAE,IAAAE,0CAAuB,EAAC,IAAI,EAAEF,QAAQ,CAAC;EACvD;AACF;AAACG,OAAA,CAAAC,OAAA,GAAAR,WAAA;AAAA", "ignoreList": []}