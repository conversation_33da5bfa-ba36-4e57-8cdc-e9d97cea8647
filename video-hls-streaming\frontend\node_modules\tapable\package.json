{"name": "tapable", "version": "2.2.2", "author": "<PERSON> @sokra", "description": "Just a little module for plugins.", "license": "MIT", "homepage": "https://github.com/webpack/tapable", "repository": {"type": "git", "url": "http://github.com/webpack/tapable.git"}, "devDependencies": {"@babel/core": "^7.4.4", "@babel/preset-env": "^7.4.4", "babel-jest": "^24.8.0", "jest": "^24.8.0", "prettier": "^3.5.3", "prettier-1": "npm:prettier@^1"}, "engines": {"node": ">=6"}, "files": ["lib", "!lib/__tests__", "tapable.d.ts"], "main": "lib/index.js", "types": "./tapable.d.ts", "browser": {"util": "./lib/util-browser.js"}, "scripts": {"lint": "yarn fmt:check", "fmt": "yarn fmt:base --log-level warn --write", "fmt:check": "yarn fmt:base --check", "fmt:base": "node node_modules/prettier/bin/prettier.cjs --cache --ignore-unknown .", "test": "jest"}, "jest": {"transform": {"__tests__[\\\\/].+\\.js$": "babel-jest"}}}