import React, { useState } from "react";
import VideoUpload from "./components/VideoUpload";
import VideoList from "./components/VideoList";
import VideoPlayer from "./components/VideoPlayer";

function App() {
  const [currentView, setCurrentView] = useState("upload");
  const [selectedVideo, setSelectedVideo] = useState(null);

  const handleVideoSelect = (video) => {
    setSelectedVideo(video);
    setCurrentView("player");
  };

  const handleVideoUploaded = () => {
    // Refresh video list when a new video is uploaded
    if (currentView === "list") {
      window.location.reload(); // Simple refresh for now
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <header className="bg-black/20 backdrop-blur-sm border-b border-white/10">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                <svg
                  className="w-6 h-6 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM5 8a1 1 0 000 2h8a1 1 0 100-2H5z" />
                </svg>
              </div>
              <h1 className="text-2xl font-bold text-white">StreamHub</h1>
            </div>
            <nav className="flex space-x-1">
              <button
                onClick={() => setCurrentView("upload")}
                className={`px-4 py-2 rounded-lg font-medium transition-all ${
                  currentView === "upload"
                    ? "bg-purple-600 text-white shadow-lg"
                    : "text-gray-300 hover:text-white hover:bg-white/10"
                }`}
              >
                Upload
              </button>
              <button
                onClick={() => setCurrentView("list")}
                className={`px-4 py-2 rounded-lg font-medium transition-all ${
                  currentView === "list"
                    ? "bg-purple-600 text-white shadow-lg"
                    : "text-gray-300 hover:text-white hover:bg-white/10"
                }`}
              >
                Library
              </button>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-6 py-8">
        {currentView === "upload" && (
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-white mb-4">
                Upload Your Videos
              </h2>
              <p className="text-gray-300 text-lg">
                Support for all video formats: MP4, AVI, MOV, MKV, WMV, FLV,
                WEBM and more
              </p>
            </div>
            <VideoUpload onVideoUploaded={handleVideoUploaded} />
          </div>
        )}

        {currentView === "list" && (
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-white mb-4">
                Video Library
              </h2>
              <p className="text-gray-300 text-lg">
                Browse and play your uploaded videos
              </p>
            </div>
            <VideoList onVideoSelect={handleVideoSelect} />
          </div>
        )}

        {currentView === "player" && selectedVideo && (
          <div className="max-w-6xl mx-auto">
            <div className="mb-6">
              <button
                onClick={() => setCurrentView("list")}
                className="flex items-center space-x-2 text-gray-300 hover:text-white transition-colors"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
                <span>Back to Library</span>
              </button>
            </div>
            <div className="bg-black/20 backdrop-blur-sm rounded-2xl border border-white/10 p-6">
              <h2 className="text-2xl font-bold text-white mb-6">
                {selectedVideo.name}
              </h2>
              <VideoPlayer
                videoId={selectedVideo.id}
                videoName={selectedVideo.name}
                streamUrl={`http://localhost:8080${selectedVideo.streamUrl}`}
              />
            </div>
          </div>
        )}
      </main>
    </div>
  );
}

export default App;
