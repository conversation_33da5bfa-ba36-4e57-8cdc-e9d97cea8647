import React, { useEffect, useRef, useState } from "react";
import videojs from "video.js";
import "video.js/dist/video-js.css";

const VideoPlayer = ({
  src,
  poster,
  onReady,
  onTimeUpdate,
  onEnded,
  className = "",
}) => {
  const videoRef = useRef(null);
  const playerRef = useRef(null);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    // Make sure Video.js player is only initialized once
    if (!playerRef.current) {
      const videoElement = document.createElement("video-js");
      videoElement.classList.add("vjs-big-play-centered");
      videoRef.current.appendChild(videoElement);

      const player = (playerRef.current = videojs(
        videoElement,
        {
          autoplay: false,
          controls: true,
          responsive: true,
          fluid: true,
          playbackRates: [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2],
          poster: poster,
          sources: [
            {
              src: src,
              type: src?.includes(".m3u8")
                ? "application/x-mpegURL"
                : "video/mp4",
            },
          ],
          html5: {
            hls: {
              enableLowInitialPlaylist: true,
              smoothQualityChange: true,
              overrideNative: true,
            },
          },
          techOrder: ["html5"],
        },
        () => {
          setIsReady(true);
          onReady && onReady(player);
        }
      ));

      // Add event listeners
      player.on("timeupdate", () => {
        onTimeUpdate && onTimeUpdate(player.currentTime());
      });

      player.on("ended", () => {
        onEnded && onEnded();
      });

      // Add quality selector if HLS
      if (src?.includes(".m3u8")) {
        player.ready(() => {
          // Add quality selector button
          const qualityButton = player.controlBar.addChild("MenuButton", {
            title: "Quality",
          });

          qualityButton.addClass("vjs-quality-selector");
          qualityButton.controlText("Quality");
        });
      }
    }

    return () => {
      const player = playerRef.current;
      if (player && !player.isDisposed()) {
        player.dispose();
        playerRef.current = null;
      }
    };
  }, []);

  // Update source when src changes
  useEffect(() => {
    const player = playerRef.current;
    if (player && src) {
      player.src({
        src: src,
        type: src.includes(".m3u8") ? "application/x-mpegURL" : "video/mp4",
      });
    }
  }, [src]);

  return (
    <div className={`video-player-wrapper ${className}`}>
      <div
        ref={videoRef}
        className="video-js-container"
        style={{ width: "100%", height: "100%" }}
      />
    </div>
  );
};

export default VideoPlayer;
