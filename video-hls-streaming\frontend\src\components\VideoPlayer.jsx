import React, { useEffect, useRef, useState } from 'react';
import Hls from 'hls.js';

const VideoPlayer = ({ videoId }) => {
  const videoRef = useRef(null);
  const hlsRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [videoInfo, setVideoInfo] = useState(null);

  useEffect(() => {
    if (!videoId || !videoRef.current) return;

    const video = videoRef.current;
    const streamUrl = `http://localhost:8080/api/videos/stream/${videoId}/index.m3u8`;

    setIsLoading(true);
    setError('');

    // Check if HLS is supported
    if (Hls.isSupported()) {
      // Create HLS instance
      const hls = new Hls({
        debug: false,
        enableWorker: true,
        lowLatencyMode: true,
        backBufferLength: 90,
      });

      hlsRef.current = hls;

      // Load the stream
      hls.loadSource(streamUrl);
      hls.attachMedia(video);

      // Event listeners
      hls.on(Hls.Events.MANIFEST_PARSED, (event, data) => {
        console.log('HLS manifest parsed, found levels:', data.levels);
        setIsLoading(false);
        setVideoInfo({
          levels: data.levels,
          duration: video.duration,
        });
      });

      hls.on(Hls.Events.ERROR, (event, data) => {
        console.error('HLS error:', data);
        if (data.fatal) {
          switch (data.type) {
            case Hls.ErrorTypes.NETWORK_ERROR:
              setError('Network error occurred while loading video');
              hls.startLoad();
              break;
            case Hls.ErrorTypes.MEDIA_ERROR:
              setError('Media error occurred while playing video');
              hls.recoverMediaError();
              break;
            default:
              setError('Fatal error occurred, cannot play video');
              hls.destroy();
              break;
          }
        }
      });

      hls.on(Hls.Events.LEVEL_SWITCHED, (event, data) => {
        console.log('Level switched to:', data.level);
      });

    } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
      // Native HLS support (Safari)
      video.src = streamUrl;
      video.addEventListener('loadedmetadata', () => {
        setIsLoading(false);
        setVideoInfo({
          duration: video.duration,
        });
      });
      video.addEventListener('error', () => {
        setError('Error loading video');
        setIsLoading(false);
      });
    } else {
      setError('HLS is not supported in this browser');
      setIsLoading(false);
    }

    // Cleanup function
    return () => {
      if (hlsRef.current) {
        hlsRef.current.destroy();
        hlsRef.current = null;
      }
    };
  }, [videoId]);

  const formatDuration = (seconds) => {
    if (!seconds || isNaN(seconds)) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleVideoError = () => {
    setError('Error loading video. Please try again.');
    setIsLoading(false);
  };

  const handleVideoLoad = () => {
    setIsLoading(false);
  };

  if (!videoId) {
    return (
      <div className="text-center text-gray-500">
        No video selected
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-bold text-gray-800 text-center">
        Video Player
      </h2>
      
      {/* Video Player Container */}
      <div className="relative bg-black rounded-lg overflow-hidden shadow-lg">
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-900 bg-opacity-75 z-10">
            <div className="text-center text-white">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
              <p>Loading video...</p>
            </div>
          </div>
        )}

        {error && (
          <div className="absolute inset-0 flex items-center justify-center bg-red-900 bg-opacity-75 z-10">
            <div className="text-center text-white">
              <svg className="mx-auto h-12 w-12 text-red-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p>{error}</p>
            </div>
          </div>
        )}

        <video
          ref={videoRef}
          controls
          className="w-full h-auto max-h-96"
          onError={handleVideoError}
          onLoadedData={handleVideoLoad}
          poster="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='800' height='450' viewBox='0 0 800 450'%3E%3Crect width='800' height='450' fill='%23000'/%3E%3Ctext x='400' y='225' font-family='Arial' font-size='24' fill='%23fff' text-anchor='middle' dominant-baseline='middle'%3ELoading...%3C/text%3E%3C/svg%3E"
        >
          Your browser does not support the video tag.
        </video>
      </div>

      {/* Video Information */}
      {videoInfo && !error && (
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-medium text-gray-800 mb-2">Video Information:</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
            <div>
              <strong>Video ID:</strong> {videoId}
            </div>
            {videoInfo.duration && (
              <div>
                <strong>Duration:</strong> {formatDuration(videoInfo.duration)}
              </div>
            )}
            {videoInfo.levels && (
              <div>
                <strong>Available Qualities:</strong> {videoInfo.levels.length} levels
              </div>
            )}
            <div>
              <strong>Format:</strong> HLS (HTTP Live Streaming)
            </div>
          </div>
        </div>
      )}

      {/* Stream URL for debugging */}
      <div className="bg-blue-50 p-4 rounded-lg">
        <h3 className="font-medium text-blue-800 mb-2">Stream URL:</h3>
        <code className="text-sm text-blue-600 break-all">
          http://localhost:8080/api/videos/stream/{videoId}/index.m3u8
        </code>
      </div>
    </div>
  );
};

export default VideoPlayer;
