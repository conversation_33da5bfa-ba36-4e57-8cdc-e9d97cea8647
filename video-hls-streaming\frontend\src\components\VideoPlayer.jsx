import React, { useEffect, useRef, useState } from "react";
import videojs from "video.js";
import "video.js/dist/video-js.css";

const VideoPlayer = ({
  src,
  poster,
  onReady,
  onTimeUpdate,
  onEnded,
  className = "",
}) => {
  const videoRef = useRef(null);
  const playerRef = useRef(null);
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    // Make sure Video.js player is only initialized once
    if (!playerRef.current) {
      const videoElement = document.createElement("video-js");
      videoElement.classList.add("vjs-big-play-centered");
      videoRef.current.appendChild(videoElement);

      const player = (playerRef.current = videojs(
        videoElement,
        {
          autoplay: false,
          controls: true,
          responsive: true,
          fluid: true,
          playbackRates: [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2],
          poster: poster,
          sources: [
            {
              src: src,
              type: src?.includes(".m3u8")
                ? "application/x-mpegURL"
                : "video/mp4",
            },
          ],
          html5: {
            hls: {
              enableLowInitialPlaylist: true,
              smoothQualityChange: true,
              overrideNative: !videojs.browser.IS_SAFARI, // Use native HLS on Safari
              withCredentials: false,
            },
            nativeVideoTracks: false,
            nativeAudioTracks: false,
            nativeTextTracks: false,
          },
          techOrder: ["html5"],
          // Error handling
          errorDisplay: true,
          preload: "metadata",
        },
        () => {
          setIsReady(true);
          onReady && onReady(player);
        }
      ));

      // Add event listeners
      player.on("timeupdate", () => {
        onTimeUpdate && onTimeUpdate(player.currentTime());
      });

      player.on("ended", () => {
        onEnded && onEnded();
      });

      // Add comprehensive error handling with retry limit
      let errorRetryCount = 0;
      const maxRetries = 2;

      player.on("error", function () {
        const error = player.error();
        console.error(
          "Video.js error:",
          error,
          "Retry count:",
          errorRetryCount
        );

        // Prevent infinite loop - limit retries
        if (errorRetryCount >= maxRetries) {
          console.error("Max retries reached. Video playback failed.");
          player.error({
            code: 4,
            message: "Video playback failed after multiple attempts",
          });
          return;
        }

        // Try fallback if HLS fails
        if (error && (error.code === 4 || error.code === 2)) {
          errorRetryCount++;
          console.log(
            `HLS failed, trying MP4 fallback... (Attempt ${errorRetryCount})`
          );
          const videoId = src.split("/stream/")[1]?.split("/")[0];
          if (videoId) {
            const mp4Src = `http://localhost:8080/api/videos/stream/${videoId}/video.mp4`;

            // Add delay to prevent rapid retries
            setTimeout(() => {
              player.src([
                {
                  src: mp4Src,
                  type: "video/mp4",
                },
              ]);
            }, 1000 * errorRetryCount); // Increasing delay
          }
        }
      });

      // Handle loading events
      player.on("loadstart", function () {
        console.log("Video loading started");
      });

      player.on("canplay", function () {
        console.log("Video can start playing");
      });

      player.on("waiting", function () {
        console.log("Video is buffering");
      });

      // Add quality selector if HLS
      if (src?.includes(".m3u8")) {
        player.ready(() => {
          // Add quality selector button
          const qualityButton = player.controlBar.addChild("MenuButton", {
            title: "Quality",
          });

          qualityButton.addClass("vjs-quality-selector");
          qualityButton.controlText("Quality");
        });
      }
    }

    return () => {
      const player = playerRef.current;
      if (player && !player.isDisposed()) {
        player.dispose();
        playerRef.current = null;
      }
    };
  }, []);

  // Update source when src changes
  useEffect(() => {
    const player = playerRef.current;
    if (player && src) {
      player.src({
        src: src,
        type: src.includes(".m3u8") ? "application/x-mpegURL" : "video/mp4",
      });
    }
  }, [src]);

  return (
    <div className={`video-player-wrapper ${className}`}>
      <div
        ref={videoRef}
        className="video-js-container"
        style={{ width: "100%", height: "100%" }}
      />
    </div>
  );
};

export default VideoPlayer;
