import React, { useState } from 'react';
import axios from 'axios';
import VideoPlayer from './VideoPlayer';

const VideoUpload = () => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [videoId, setVideoId] = useState(null);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      if (file.type !== 'video/mp4') {
        setError('Please select an MP4 video file');
        return;
      }
      
      // Validate file size (max 500MB)
      if (file.size > 500 * 1024 * 1024) {
        setError('File size must be less than 500MB');
        return;
      }
      
      setSelectedFile(file);
      setError('');
      setSuccess('');
      setVideoId(null);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      setError('Please select a video file first');
      return;
    }

    setUploading(true);
    setUploadProgress(0);
    setError('');
    setSuccess('');

    const formData = new FormData();
    formData.append('video', selectedFile);

    try {
      const response = await axios.post('http://localhost:8080/api/videos/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(progress);
        },
      });

      if (response.data.videoId) {
        setVideoId(response.data.videoId);
        setSuccess('Video uploaded and processed successfully!');
        setSelectedFile(null);
        // Reset file input
        document.getElementById('video-input').value = '';
      } else {
        setError('Upload failed: ' + (response.data.error || 'Unknown error'));
      }
    } catch (err) {
      console.error('Upload error:', err);
      setError('Upload failed: ' + (err.response?.data?.error || err.message));
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  const resetUpload = () => {
    setSelectedFile(null);
    setVideoId(null);
    setError('');
    setSuccess('');
    setUploadProgress(0);
    document.getElementById('video-input').value = '';
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h1 className="text-3xl font-bold text-center mb-8 text-gray-800">
        Video HLS Streaming
      </h1>

      {!videoId ? (
        <div className="space-y-6">
          {/* File Upload Section */}
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
            <div className="space-y-4">
              <div className="text-gray-600">
                <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                  <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
              </div>
              <div>
                <label htmlFor="video-input" className="cursor-pointer">
                  <span className="text-lg font-medium text-blue-600 hover:text-blue-500">
                    Choose MP4 video file
                  </span>
                  <input
                    id="video-input"
                    type="file"
                    accept="video/mp4"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                </label>
                <p className="text-sm text-gray-500 mt-2">
                  Maximum file size: 500MB
                </p>
              </div>
            </div>
          </div>

          {/* Selected File Info */}
          {selectedFile && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-medium text-gray-800 mb-2">Selected File:</h3>
              <p className="text-sm text-gray-600">
                <strong>Name:</strong> {selectedFile.name}
              </p>
              <p className="text-sm text-gray-600">
                <strong>Size:</strong> {(selectedFile.size / (1024 * 1024)).toFixed(2)} MB
              </p>
              <p className="text-sm text-gray-600">
                <strong>Type:</strong> {selectedFile.type}
              </p>
            </div>
          )}

          {/* Upload Button */}
          <div className="flex justify-center space-x-4">
            <button
              onClick={handleUpload}
              disabled={!selectedFile || uploading}
              className={`px-6 py-3 rounded-lg font-medium ${
                !selectedFile || uploading
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700'
              }`}
            >
              {uploading ? 'Processing...' : 'Upload & Convert to HLS'}
            </button>
            
            {selectedFile && (
              <button
                onClick={resetUpload}
                className="px-6 py-3 rounded-lg font-medium bg-gray-600 text-white hover:bg-gray-700"
              >
                Reset
              </button>
            )}
          </div>

          {/* Upload Progress */}
          {uploading && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm text-gray-600">
                <span>Uploading...</span>
                <span>{uploadProgress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadProgress}%` }}
                ></div>
              </div>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-800">{error}</p>
            </div>
          )}

          {/* Success Message */}
          {success && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <p className="text-green-800">{success}</p>
            </div>
          )}
        </div>
      ) : (
        <div className="space-y-6">
          {/* Video Player Section */}
          <VideoPlayer videoId={videoId} />
          
          {/* Upload Another Video Button */}
          <div className="text-center">
            <button
              onClick={resetUpload}
              className="px-6 py-3 rounded-lg font-medium bg-blue-600 text-white hover:bg-blue-700"
            >
              Upload Another Video
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoUpload;
