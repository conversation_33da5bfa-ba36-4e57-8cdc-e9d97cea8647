import React, { useState, useEffect } from "react";
import VideoPlayer from "./VideoPlayer";

const VideoWatch = ({ video, user, onBack }) => {
  const [liked, setLiked] = useState(false);
  const [disliked, setDisliked] = useState(false);
  const [subscribed, setSubscribed] = useState(false);
  const [comments, setComments] = useState([]);
  const [newComment, setNewComment] = useState("");
  const [showDescription, setShowDescription] = useState(false);

  useEffect(() => {
    if (video && user) {
      // TODO: Check if user has liked/disliked the video
      // TODO: Check if user is subscribed to the channel
      // TODO: Load comments
    }
  }, [video, user]);

  const handleLike = async () => {
    if (!user) return;

    try {
      const response = await fetch(
        `http://localhost:8080/api/videos/${video.videoId}/like`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ type: "LIKE" }),
        }
      );

      if (response.ok) {
        setLiked(!liked);
        if (disliked) setDisliked(false);
      } else if (response.status === 401) {
        alert("Please sign in to like videos");
        localStorage.removeItem("token");
        localStorage.removeItem("user");
      } else {
        console.error(
          "Error liking video:",
          response.status,
          response.statusText
        );
      }
    } catch (error) {
      console.error("Error liking video:", error);
    }
  };

  const handleDislike = async () => {
    if (!user) return;

    try {
      const response = await fetch(
        `http://localhost:8080/api/videos/${video.videoId}/like`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ type: "DISLIKE" }),
        }
      );

      if (response.ok) {
        setDisliked(!disliked);
        if (liked) setLiked(false);
      } else if (response.status === 401) {
        alert("Please sign in to dislike videos");
        localStorage.removeItem("token");
        localStorage.removeItem("user");
      } else {
        console.error(
          "Error disliking video:",
          response.status,
          response.statusText
        );
      }
    } catch (error) {
      console.error("Error disliking video:", error);
    }
  };

  const handleSubscribe = async () => {
    if (!user || !video.user) return;

    try {
      const response = await fetch(
        `http://localhost:8080/api/channels/${video.user.id}/subscribe`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        }
      );

      if (response.ok) {
        setSubscribed(!subscribed);
      }
    } catch (error) {
      console.error("Error subscribing:", error);
    }
  };

  const handleCommentSubmit = async (e) => {
    e.preventDefault();
    if (!user || !newComment.trim()) return;

    try {
      const response = await fetch(
        `http://localhost:8080/api/videos/${video.videoId}/comment`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ content: newComment.trim() }),
        }
      );

      if (response.ok) {
        setNewComment("");
        // TODO: Reload comments
      }
    } catch (error) {
      console.error("Error posting comment:", error);
    }
  };

  const formatViewCount = (count) => {
    if (!count) return "0 views";
    if (count < 1000) return `${count} views`;
    if (count < 1000000) return `${(count / 1000).toFixed(1)}K views`;
    return `${(count / 1000000).toFixed(1)}M views`;
  };

  const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  if (!video) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">Video not found</p>
        <button
          onClick={onBack}
          className="youtube-button youtube-button-secondary mt-4"
        >
          Go Back
        </button>
      </div>
    );
  }

  const videoSrc = `http://localhost:8080/api/videos/stream/${video.videoId}/playlist.m3u8`;
  const posterSrc = video.thumbnailUrl
    ? `http://localhost:8080${video.thumbnailUrl}`
    : null;

  return (
    <div className="max-w-7xl mx-auto">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Video Section */}
        <div className="lg:col-span-2 space-y-4">
          {/* Video Player */}
          <div className="bg-black rounded-lg overflow-hidden">
            <VideoPlayer
              src={videoSrc}
              poster={posterSrc}
              className="w-full aspect-video"
              onReady={(player) => {
                console.log("Video player ready");
              }}
            />
          </div>

          {/* Video Info */}
          <div className="space-y-4">
            <h1 className="text-xl font-bold text-gray-900">
              {video.title || "Untitled Video"}
            </h1>

            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-600">
                {formatViewCount(video.viewCount)} •{" "}
                {formatDate(video.createdAt)}
              </div>

              {/* Action Buttons */}
              <div className="flex items-center space-x-2">
                <div className="flex items-center bg-gray-100 rounded-full">
                  <button
                    onClick={handleLike}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-l-full hover:bg-gray-200 ${
                      liked ? "text-blue-600" : "text-gray-700"
                    }`}
                  >
                    <svg
                      className="w-5 h-5"
                      fill={liked ? "currentColor" : "none"}
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5"
                      />
                    </svg>
                    <span>{video.likeCount || 0}</span>
                  </button>
                  <div className="w-px h-6 bg-gray-300"></div>
                  <button
                    onClick={handleDislike}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-r-full hover:bg-gray-200 ${
                      disliked ? "text-blue-600" : "text-gray-700"
                    }`}
                  >
                    <svg
                      className="w-5 h-5"
                      fill={disliked ? "currentColor" : "none"}
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M10 14H5.236a2 2 0 01-1.789-2.894l3.5-7A2 2 0 018.736 3h4.018c.163 0 .326.02.485.06L17 4m-7 10v5a2 2 0 002 2h.095c.5 0 .905-.405.905-.905 0-.714.211-1.412.608-2.006L17 13V4m-7 10h2m5-10H5a2 2 0 00-2 2v6a2 2 0 002 2h2.5"
                      />
                    </svg>
                    <span>{video.dislikeCount || 0}</span>
                  </button>
                </div>

                <button className="flex items-center space-x-2 px-4 py-2 bg-gray-100 rounded-full hover:bg-gray-200">
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
                    />
                  </svg>
                  <span>Share</span>
                </button>
              </div>
            </div>

            {/* Channel Info */}
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium">
                  {video.user?.displayName?.charAt(0).toUpperCase() ||
                    video.user?.username?.charAt(0).toUpperCase() ||
                    "U"}
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">
                    {video.user?.displayName ||
                      video.user?.username ||
                      "Unknown Channel"}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {video.user?.subscriberCount || 0} subscribers
                  </p>
                </div>
              </div>

              {user && video.user?.id !== user.id && (
                <button
                  onClick={handleSubscribe}
                  className={`youtube-button ${
                    subscribed
                      ? "youtube-button-secondary"
                      : "youtube-button-primary"
                  }`}
                >
                  {subscribed ? "Subscribed" : "Subscribe"}
                </button>
              )}
            </div>

            {/* Description */}
            {video.description && (
              <div className="bg-gray-50 rounded-lg p-4">
                <div
                  className={`text-sm text-gray-700 ${
                    showDescription ? "" : "line-clamp-3"
                  }`}
                >
                  {video.description}
                </div>
                {video.description.length > 200 && (
                  <button
                    onClick={() => setShowDescription(!showDescription)}
                    className="text-sm text-gray-600 hover:text-gray-800 mt-2"
                  >
                    {showDescription ? "Show less" : "Show more"}
                  </button>
                )}
              </div>
            )}

            {/* Comments Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Comments</h3>

              {user && (
                <form onSubmit={handleCommentSubmit} className="space-y-3">
                  <div className="flex space-x-3">
                    <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-medium text-sm">
                      {user.displayName?.charAt(0).toUpperCase() ||
                        user.username?.charAt(0).toUpperCase()}
                    </div>
                    <div className="flex-1">
                      <textarea
                        value={newComment}
                        onChange={(e) => setNewComment(e.target.value)}
                        placeholder="Add a comment..."
                        className="w-full px-0 py-2 border-0 border-b-2 border-gray-200 focus:border-blue-500 focus:outline-none resize-none"
                        rows={1}
                      />
                    </div>
                  </div>
                  {newComment.trim() && (
                    <div className="flex justify-end space-x-2">
                      <button
                        type="button"
                        onClick={() => setNewComment("")}
                        className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        className="px-4 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                      >
                        Comment
                      </button>
                    </div>
                  )}
                </form>
              )}

              {/* Comments List */}
              <div className="space-y-4">
                {comments.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">
                    No comments yet. Be the first to comment!
                  </p>
                ) : (
                  comments.map((comment) => (
                    <div key={comment.id} className="flex space-x-3">
                      <div className="w-8 h-8 bg-gray-400 rounded-full flex items-center justify-center text-white font-medium text-sm">
                        {comment.user?.displayName?.charAt(0).toUpperCase() ||
                          "U"}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-1">
                          <span className="font-medium text-sm">
                            {comment.user?.displayName}
                          </span>
                          <span className="text-xs text-gray-500">
                            {formatDate(comment.createdAt)}
                          </span>
                        </div>
                        <p className="text-sm text-gray-700">
                          {comment.content}
                        </p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar - Related Videos */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-900">Up next</h3>
          <div className="space-y-3">
            {/* TODO: Add related videos */}
            <p className="text-gray-500 text-sm">
              Related videos will appear here
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoWatch;
