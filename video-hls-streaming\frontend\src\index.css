/* Video.js custom styles */
@import "video.js/dist/video-js.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

/* YouTube-style custom styles */
@layer base {
  body {
    font-family: "Roboto", "Arial", sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f9f9f9;
    color: #0f0f0f;
  }
}

@layer components {
  .youtube-header {
    @apply bg-white border-b border-gray-200 sticky top-0 z-50;
  }

  .youtube-sidebar {
    @apply bg-white border-r border-gray-200 h-screen sticky top-16 overflow-y-auto;
  }

  .youtube-video-card {
    @apply bg-white rounded-lg overflow-hidden hover:shadow-md transition-shadow duration-200;
  }

  .youtube-button {
    @apply px-4 py-2 rounded-full font-medium transition-colors duration-200;
  }

  .youtube-button-primary {
    @apply bg-youtube-red text-white hover:bg-youtube-red-dark;
  }

  .youtube-button-secondary {
    @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
  }

  .youtube-input {
    @apply border border-gray-300 rounded-full px-4 py-2 focus:outline-none focus:border-blue-500;
  }
}

/* Video.js player customization */
.video-js {
  font-family: "Roboto", Arial, sans-serif;
}

.video-js .vjs-big-play-button {
  background-color: rgba(0, 0, 0, 0.7);
  border: none;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  line-height: 80px;
  font-size: 2.5em;
  left: 50%;
  top: 50%;
  margin-left: -40px;
  margin-top: -40px;
}

.video-js .vjs-control-bar {
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.3) 70%,
    rgba(0, 0, 0, 0) 100%
  );
}

.video-js .vjs-progress-control .vjs-progress-holder {
  height: 6px;
}

.video-js .vjs-progress-control .vjs-play-progress {
  background-color: #ff0000;
}

.video-js .vjs-volume-panel.vjs-volume-panel-horizontal .vjs-volume-control {
  width: 8em;
}
